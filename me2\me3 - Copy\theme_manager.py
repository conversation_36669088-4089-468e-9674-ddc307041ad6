# Theme Manager Integration for EpiNn0x Trader Interface
# Centralized theme management and application

from PySide6.QtCore import QObject, Signal, QSettings
from PySide6.QtWidgets import QApplication
from modern_themes import ThemeManager, THEMES
import pyqtgraph as pg

class ApplicationThemeManager(QObject):
    """Centralized theme manager for the entire application"""
    
    theme_applied = Signal(str)  # Emits theme name when applied
    
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        self.theme_manager = ThemeManager()
        self.settings = QSettings("EpiNn0x", "TraderInterface")
        
        # Connect theme manager signals
        self.theme_manager.theme_changed.connect(self.apply_theme_to_application)
        
        # Load saved theme
        self.load_saved_theme()
    
    def load_saved_theme(self):
        """Load previously saved theme"""
        saved_theme = self.settings.value("theme", "dark_professional")
        if saved_theme in THEMES:
            self.set_theme(saved_theme)
        else:
            self.set_theme("dark_professional")
    
    def save_theme(self, theme_name):
        """Save current theme to settings"""
        self.settings.setValue("theme", theme_name)
    
    def set_theme(self, theme_name):
        """Set and apply theme"""
        if self.theme_manager.set_theme(theme_name):
            self.save_theme(theme_name)
            return True
        return False
    
    def get_current_theme_name(self):
        """Get current theme name"""
        return self.theme_manager.theme_name
    
    def get_available_themes(self):
        """Get list of available themes"""
        return [(name, THEMES[name].name) for name in THEMES.keys()]
    
    def apply_theme_to_application(self, theme_name):
        """Apply theme to the entire application"""
        theme = self.theme_manager.get_current_theme()
        
        # Apply to main window
        if self.main_window:
            stylesheet = self.theme_manager.get_complete_stylesheet()
            self.main_window.setStyleSheet(stylesheet)
        
        # Apply to PyQtGraph
        self.apply_theme_to_pyqtgraph(theme)
        
        # Apply to application
        app = QApplication.instance()
        if app:
            # Set application-wide stylesheet for dialogs and popups
            app.setStyleSheet(self.get_application_stylesheet(theme))
        
        # Emit signal
        self.theme_applied.emit(theme_name)
    
    def apply_theme_to_pyqtgraph(self, theme):
        """Apply theme colors to PyQtGraph"""
        try:
            # Set PyQtGraph background and foreground
            pg.setConfigOption('background', theme.get_color('background'))
            pg.setConfigOption('foreground', theme.get_color('text'))
            
            # Set default pen colors
            pg.setConfigOption('antialias', True)
            
            # Update existing plots if main window is available
            if self.main_window and hasattr(self.main_window, 'update_chart_theme'):
                self.main_window.update_chart_theme(theme)
                
        except Exception as e:
            print(f"Error applying theme to PyQtGraph: {e}")
    
    def get_application_stylesheet(self, theme):
        """Get stylesheet for application-wide elements"""
        return f"""
        /* Application-wide styles for dialogs and popups */
        QDialog {{
            background-color: {theme.get_color('background')};
            color: {theme.get_color('text')};
        }}
        
        QMessageBox {{
            background-color: {theme.get_color('surface')};
            color: {theme.get_color('text')};
        }}
        
        QMessageBox QPushButton {{
            background-color: {theme.get_color('primary')};
            color: {theme.get_color('background')};
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }}
        
        QMessageBox QPushButton:hover {{
            background-color: {theme.get_color('primary_hover')};
        }}
        
        QFileDialog {{
            background-color: {theme.get_color('background')};
            color: {theme.get_color('text')};
        }}
        
        QInputDialog {{
            background-color: {theme.get_color('surface')};
            color: {theme.get_color('text')};
        }}
        
        /* Tooltip styling */
        QToolTip {{
            background-color: {theme.get_color('surface_elevated')};
            color: {theme.get_color('text')};
            border: 1px solid {theme.get_color('border')};
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
        }}
        
        /* Scrollbar styling */
        QScrollBar:vertical {{
            background-color: {theme.get_color('surface')};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {theme.get_color('border')};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {theme.get_color('primary')};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}
        
        QScrollBar:horizontal {{
            background-color: {theme.get_color('surface')};
            height: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {theme.get_color('border')};
            border-radius: 6px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {theme.get_color('primary')};
        }}
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            border: none;
            background: none;
        }}
        """
    
    def get_theme_colors(self):
        """Get current theme colors for external use"""
        return self.theme_manager.get_current_theme().colors
    
    def create_theme_menu_actions(self, menu, callback=None):
        """Create theme menu actions for a menu"""
        theme_actions = []
        current_theme = self.get_current_theme_name()
        
        for theme_key, theme_name in self.get_available_themes():
            action = menu.addAction(theme_name)
            action.setCheckable(True)
            action.setChecked(theme_key == current_theme)
            
            # Connect to theme change
            action.triggered.connect(lambda checked, t=theme_key: self.set_theme(t))
            
            # Connect to callback if provided
            if callback:
                action.triggered.connect(callback)
            
            theme_actions.append(action)
        
        return theme_actions
    
    def update_theme_menu_actions(self, actions):
        """Update theme menu actions checked state"""
        current_theme = self.get_current_theme_name()
        theme_keys = [key for key, _ in self.get_available_themes()]
        
        for i, action in enumerate(actions):
            if i < len(theme_keys):
                action.setChecked(theme_keys[i] == current_theme)

class ThemeAwareWidget:
    """Mixin class for widgets that need to respond to theme changes"""
    
    def __init__(self, theme_manager):
        self.theme_manager = theme_manager
        self.theme_manager.theme_applied.connect(self.on_theme_changed)
    
    def on_theme_changed(self, theme_name):
        """Override this method to handle theme changes"""
        pass
    
    def get_current_theme(self):
        """Get current theme"""
        return self.theme_manager.theme_manager.get_current_theme()
    
    def get_theme_color(self, color_key):
        """Get specific color from current theme"""
        return self.get_current_theme().get_color(color_key)

def apply_trading_button_properties(button, action_type):
    """Apply trading-specific properties to buttons"""
    button.setProperty("action", action_type)
    
    # Add tooltip based on action type
    tooltips = {
        "buy": "Execute Buy Order",
        "sell": "Execute Sell Order",
        "close": "Close Position",
        "cancel": "Cancel Order"
    }
    
    if action_type in tooltips:
        button.setToolTip(tooltips[action_type])

def apply_status_properties(label, status_type):
    """Apply status-specific properties to labels"""
    label.setProperty("status", status_type)
    
    # Add tooltip based on status type
    tooltips = {
        "profit": "Profitable Position",
        "loss": "Loss Position", 
        "neutral": "Neutral Position",
        "warning": "Warning Status",
        "info": "Information Status"
    }
    
    if status_type in tooltips:
        label.setToolTip(tooltips[status_type])

# Global theme manager instance
_global_theme_manager = None

def get_global_theme_manager():
    """Get global theme manager instance"""
    global _global_theme_manager
    return _global_theme_manager

def set_global_theme_manager(theme_manager):
    """Set global theme manager instance"""
    global _global_theme_manager
    _global_theme_manager = theme_manager
