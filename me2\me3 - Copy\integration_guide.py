# Integration Guide for Enhanced UI Components
# Add these improvements to your me2_stable.py

def integrate_ui_improvements(self):
    """Add modern UI improvements to your existing interface"""
    
    # 1. Apply modern theme system
    from ui_improvements import generate_modern_stylesheet, MODERN_THEMES
    
    # Add theme selector to your existing theme menu
    self.current_theme = 'dark_professional'
    self.apply_modern_theme()
    
    # 2. Replace existing dashboard with enhanced version
    from enhanced_components import ModernDashboardWidget
    
    # Replace your dashboard tab
    if hasattr(self, 'dashboard_tab'):
        self.main_tabs.removeTab(0)  # Remove old dashboard
    
    self.enhanced_dashboard = ModernDashboardWidget(self)
    self.main_tabs.insertTab(0, self.enhanced_dashboard, "📊 Dashboard")
    
    # 3. Add notification center to sidebar
    from notifications import NotificationCenter, AlertManager
    
    self.notification_center = NotificationCenter(self)
    self.alert_manager = AlertManager(self.notification_center)
    
    # Add to left panel
    if hasattr(self, 'left_layout'):
        self.left_layout.addWidget(self.notification_center)
    
    # 4. Enhanced chart widget
    from enhanced_chart import ModernChartWidget
    
    # Replace existing chart
    if hasattr(self, 'chart'):
        # Save current settings
        current_symbol = self.get_symbol() if hasattr(self, 'get_symbol') else None
        
        # Replace with modern chart
        self.modern_chart = ModernChartWidget(self)
        
        # Add to right panel (replace existing chart)
        if hasattr(self, 'right_splitter'):
            # Find and replace chart widget
            for i in range(self.right_splitter.count()):
                widget = self.right_splitter.widget(i)
                if hasattr(widget, 'chart'):  # Assuming your chart container has 'chart' attribute
                    self.right_splitter.replaceWidget(i, self.modern_chart)
                    break
    
    # 5. Enhanced trading controls
    from enhanced_components import TradingControlPanel, RiskManagementWidget
    
    # Replace left panel trading controls
    self.enhanced_trading_panel = TradingControlPanel(self)
    self.risk_management_panel = RiskManagementWidget(self)
    
    # Add to left panel
    if hasattr(self, 'left_layout'):
        # Remove old trading controls if they exist
        # self.left_layout.removeWidget(old_trading_widget)  # Uncomment and specify your old widget
        
        self.left_layout.addWidget(self.enhanced_trading_panel)
        self.left_layout.addWidget(self.risk_management_panel)

def apply_modern_theme(self):
    """Apply modern theme to the interface"""
    from ui_improvements import generate_modern_stylesheet
    
    stylesheet = generate_modern_stylesheet(self.current_theme)
    self.setStyleSheet(stylesheet)
    
    # Add theme selector to menu if not exists
    if not hasattr(self, 'theme_selector_added'):
        self.add_theme_selector_to_menu()
        self.theme_selector_added = True

def add_theme_selector_to_menu(self):
    """Add theme selector to existing menu"""
    from ui_improvements import MODERN_THEMES
    
    # Find your existing theme menu or create new one
    theme_menu = None
    for action in self.menuBar().actions():
        if action.text() == "Settings":
            settings_menu = action.menu()
            for sub_action in settings_menu.actions():
                if "Theme" in sub_action.text():
                    theme_menu = sub_action.menu()
                    break
    
    if theme_menu:
        # Clear existing theme actions
        theme_menu.clear()
        
        # Add new theme options
        theme_group = QActionGroup(self)
        
        for theme_key, theme_data in MODERN_THEMES.items():
            action = QAction(theme_data['name'], self)
            action.setCheckable(True)
            action.setData(theme_key)
            if theme_key == self.current_theme:
                action.setChecked(True)
            
            action.triggered.connect(lambda checked, key=theme_key: self.change_theme(key))
            theme_group.addAction(action)
            theme_menu.addAction(action)

def change_theme(self, theme_key):
    """Change the application theme"""
    self.current_theme = theme_key
    self.apply_modern_theme()
    
    # Save theme preference
    if hasattr(self, 'save_settings'):
        self.save_settings()

def setup_enhanced_notifications(self):
    """Setup enhanced notification system"""
    # Connect existing update methods to notification checks
    
    # Example: Check alerts when updating positions
    original_update_positions = self._update_positions
    
    def enhanced_update_positions():
        result = original_update_positions()
        
        # Check position alerts
        try:
            positions = self.get_current_positions()  # Implement this method
            self.alert_manager.check_position_alerts(positions)
        except Exception as e:
            print(f"Error checking position alerts: {e}")
        
        return result
    
    self._update_positions = enhanced_update_positions
    
    # Check P&L alerts
    original_update_account = self._update_account
    
    def enhanced_update_account():
        result = original_update_account()
        
        # Check P&L alerts
        try:
            total_pnl = self.get_total_pnl()  # Implement this method
            self.alert_manager.check_pnl_alerts(total_pnl)
        except Exception as e:
            print(f"Error checking P&L alerts: {e}")
        
        return result
    
    self._update_account = enhanced_update_account

def add_keyboard_shortcuts(self):
    """Add enhanced keyboard shortcuts"""
    # Add new shortcuts in addition to existing ones
    
    # Theme switching
    QShortcut(QKeySequence("Ctrl+Shift+T"), self, activated=self.cycle_themes)
    
    # Quick notifications
    QShortcut(QKeySequence("Ctrl+N"), self, activated=self.show_notifications)
    
    # Chart tools
    QShortcut(QKeySequence("Ctrl+Shift+L"), self, activated=self.toggle_chart_drawing_mode)
    
    # Risk management
    QShortcut(QKeySequence("Ctrl+R"), self, activated=self.show_risk_calculator)

def cycle_themes(self):
    """Cycle through available themes"""
    from ui_improvements import MODERN_THEMES
    
    theme_keys = list(MODERN_THEMES.keys())
    current_index = theme_keys.index(self.current_theme)
    next_index = (current_index + 1) % len(theme_keys)
    
    self.change_theme(theme_keys[next_index])
    
    # Show notification
    if hasattr(self, 'notification_center'):
        theme_name = MODERN_THEMES[theme_keys[next_index]]['name']
        self.notification_center.add_notification(
            "Theme Changed",
            f"Switched to {theme_name} theme",
            'info'
        )

def show_notifications(self):
    """Show/hide notification center"""
    if hasattr(self, 'notification_center'):
        self.notification_center.setVisible(not self.notification_center.isVisible())

def toggle_chart_drawing_mode(self):
    """Toggle chart drawing mode"""
    if hasattr(self, 'modern_chart'):
        # Toggle drawing mode (implement in your chart widget)
        current_mode = getattr(self.modern_chart, 'drawing_mode', None)
        new_mode = 'trendline' if current_mode != 'trendline' else None
        self.modern_chart.drawing_mode = new_mode
        
        # Update status
        if hasattr(self, 'notification_center'):
            status = "enabled" if new_mode else "disabled"
            self.notification_center.add_notification(
                "Drawing Mode",
                f"Chart drawing mode {status}",
                'info'
            )

def show_risk_calculator(self):
    """Show risk calculation dialog"""
    if hasattr(self, 'risk_management_panel'):
        # Create risk calculator dialog
        dialog = RiskCalculatorDialog(self)
        dialog.exec()

# Helper methods to implement in your main class
def get_current_positions(self):
    """Get current positions for alert checking"""
    # Implement this to return current positions
    # Return format: [{'symbol': 'BTC/USDT', 'notional': 1000.0, 'pnl': 50.0}, ...]
    return []

def get_total_pnl(self):
    """Get total unrealized P&L"""
    # Implement this to return total P&L
    return 0.0

# Risk Calculator Dialog
class RiskCalculatorDialog(QDialog):
    """Risk calculation dialog"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_dialog()
    
    def setup_dialog(self):
        """Setup risk calculator dialog"""
        self.setWindowTitle("Risk Calculator")
        self.setFixedSize(400, 300)
        
        layout = QFormLayout(self)
        
        # Account balance
        self.account_balance = QDoubleSpinBox()
        self.account_balance.setRange(1, 1000000)
        self.account_balance.setValue(10000)
        self.account_balance.setPrefix("$")
        layout.addRow("Account Balance:", self.account_balance)
        
        # Risk percentage
        self.risk_percentage = QDoubleSpinBox()
        self.risk_percentage.setRange(0.1, 10)
        self.risk_percentage.setValue(2.0)
        self.risk_percentage.setSuffix("%")
        layout.addRow("Risk Percentage:", self.risk_percentage)
        
        # Entry price
        self.entry_price = QDoubleSpinBox()
        self.entry_price.setDecimals(4)
        self.entry_price.setRange(0.0001, 1000000)
        layout.addRow("Entry Price:", self.entry_price)
        
        # Stop loss price
        self.stop_loss = QDoubleSpinBox()
        self.stop_loss.setDecimals(4)
        self.stop_loss.setRange(0.0001, 1000000)
        layout.addRow("Stop Loss:", self.stop_loss)
        
        # Calculate button
        calc_btn = QPushButton("Calculate Position Size")
        calc_btn.clicked.connect(self.calculate)
        layout.addWidget(calc_btn)
        
        # Results
        self.risk_amount_label = QLabel("$0.00")
        layout.addRow("Risk Amount:", self.risk_amount_label)
        
        self.position_size_label = QLabel("0.0000")
        layout.addRow("Position Size:", self.position_size_label)
        
        self.risk_reward_label = QLabel("1:0")
        layout.addRow("Risk:Reward:", self.risk_reward_label)
    
    def calculate(self):
        """Calculate position size based on risk"""
        account_balance = self.account_balance.value()
        risk_pct = self.risk_percentage.value() / 100
        entry_price = self.entry_price.value()
        stop_loss = self.stop_loss.value()
        
        if entry_price > 0 and stop_loss > 0 and entry_price != stop_loss:
            risk_amount = account_balance * risk_pct
            price_diff = abs(entry_price - stop_loss)
            position_size = risk_amount / price_diff
            
            self.risk_amount_label.setText(f"${risk_amount:.2f}")
            self.position_size_label.setText(f"{position_size:.4f}")
            
            # Calculate risk:reward if take profit is set
            # For now, just show the risk amount
            self.risk_reward_label.setText("Calculated")

# Sample integration code for your __init__ method
"""
Add this to your EpinnoxTraderInterface.__init__ method after your existing initialization:

    # Apply UI improvements
    self.integrate_ui_improvements()
    
    # Setup enhanced notifications
    self.setup_enhanced_notifications()
    
    # Add enhanced keyboard shortcuts
    self.add_keyboard_shortcuts()
"""