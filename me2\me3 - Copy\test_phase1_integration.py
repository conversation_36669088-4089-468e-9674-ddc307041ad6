#!/usr/bin/env python3
"""
Test script for Phase 1 UI modernization integration
Tests the new theming system, notification center, and modern components
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from theme_manager import ApplicationThemeManager, apply_trading_button_properties, apply_status_properties
    from notification_enhancements import ModernNotificationCenter
    from ui_components import ModernCard, ModernButton, ModernProgressBar, StatusIndicator, ModernLineEdit
    print("✓ All Phase 1 modules imported successfully")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

class Phase1TestWindow(QMainWindow):
    """Test window for Phase 1 features"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_theme_manager()
        self.setup_test_timer()
    
    def setup_ui(self):
        """Setup test UI"""
        self.setWindowTitle("Phase 1 Integration Test - EpiNn0x Trader")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Notification Center
        self.notification_center = ModernNotificationCenter(self)
        self.notification_center.setFixedWidth(350)
        main_layout.addWidget(self.notification_center)
        
        # Right panel - Test components
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Theme test card
        theme_card = ModernCard("Theme Testing")
        theme_layout = QHBoxLayout()
        
        # Theme buttons
        for theme_name in ['dark_professional', 'cyberpunk', 'modern_light']:
            btn = ModernButton(theme_name.replace('_', ' ').title(), "primary")
            btn.clicked.connect(lambda checked, t=theme_name: self.change_theme(t))
            theme_layout.addWidget(btn)
        
        theme_card.add_layout(theme_layout)
        right_layout.addWidget(theme_card)
        
        # Component test card
        component_card = ModernCard("Modern Components")
        
        # Trading buttons
        trading_layout = QHBoxLayout()
        
        buy_btn = QPushButton("BUY")
        apply_trading_button_properties(buy_btn, "buy")
        buy_btn.clicked.connect(lambda: self.test_notification("trade", "success"))
        trading_layout.addWidget(buy_btn)
        
        sell_btn = QPushButton("SELL")
        apply_trading_button_properties(sell_btn, "sell")
        sell_btn.clicked.connect(lambda: self.test_notification("trade", "error"))
        trading_layout.addWidget(sell_btn)
        
        component_card.add_layout(trading_layout)
        
        # Status indicators
        status_layout = QHBoxLayout()
        
        self.online_status = StatusIndicator("online")
        status_layout.addWidget(self.online_status)
        status_layout.addWidget(QLabel("Online"))
        
        self.warning_status = StatusIndicator("warning")
        status_layout.addWidget(self.warning_status)
        status_layout.addWidget(QLabel("Warning"))
        
        self.offline_status = StatusIndicator("offline")
        status_layout.addWidget(self.offline_status)
        status_layout.addWidget(QLabel("Offline"))
        
        component_card.add_layout(status_layout)
        
        # Progress bar
        self.progress_bar = ModernProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(65)
        component_card.add_widget(self.progress_bar)
        
        # Input field
        input_field = ModernLineEdit("Enter symbol...")
        component_card.add_widget(input_field)
        
        right_layout.addWidget(component_card)
        
        # Notification test card
        notification_card = ModernCard("Notification Testing")
        
        notification_layout = QVBoxLayout()
        
        # Notification type buttons
        notification_types = [
            ("Info", "info", "normal"),
            ("Success", "success", "normal"),
            ("Warning", "warning", "high"),
            ("Error", "error", "high"),
            ("Trade Alert", "trade", "high"),
            ("Price Alert", "alert", "high")
        ]
        
        for label, ntype, priority in notification_types:
            btn = ModernButton(f"Test {label}", "default")
            btn.clicked.connect(lambda checked, t=ntype, p=priority: self.test_notification(t, p))
            notification_layout.addWidget(btn)
        
        notification_card.add_layout(notification_layout)
        right_layout.addWidget(notification_card)
        
        # Status labels test
        status_card = ModernCard("Status Labels")
        
        self.profit_label = QLabel("Profit: +$125.50")
        apply_status_properties(self.profit_label, "profit")
        status_card.add_widget(self.profit_label)
        
        self.loss_label = QLabel("Loss: -$45.20")
        apply_status_properties(self.loss_label, "loss")
        status_card.add_widget(self.loss_label)
        
        self.neutral_label = QLabel("Neutral: $0.00")
        apply_status_properties(self.neutral_label, "neutral")
        status_card.add_widget(self.neutral_label)
        
        right_layout.addWidget(status_card)
        
        right_layout.addStretch()
        main_layout.addWidget(right_panel)
    
    def setup_theme_manager(self):
        """Setup theme manager"""
        self.theme_manager = ApplicationThemeManager(self)
        
        # Apply initial theme
        self.theme_manager.apply_theme_to_application(
            self.theme_manager.get_current_theme_name()
        )
        
        print(f"✓ Theme manager initialized with theme: {self.theme_manager.get_current_theme_name()}")
    
    def setup_test_timer(self):
        """Setup timer for automatic testing"""
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.auto_test_notifications)
        self.test_timer.start(10000)  # Test every 10 seconds
        
        self.test_counter = 0
    
    def change_theme(self, theme_name):
        """Change application theme"""
        if self.theme_manager.set_theme(theme_name):
            print(f"✓ Theme changed to: {theme_name}")
            self.test_notification("info", "normal", f"Theme changed to {theme_name.replace('_', ' ').title()}")
        else:
            print(f"✗ Failed to change theme to: {theme_name}")
    
    def test_notification(self, notification_type, priority, custom_message=None):
        """Test notification system"""
        messages = {
            "info": "This is an information notification",
            "success": "Operation completed successfully",
            "warning": "Warning: Check your settings",
            "error": "Error: Connection failed",
            "trade": "Trade executed: BUY MOODENG/USDT",
            "alert": "Price alert: MOODENG reached $0.25"
        }
        
        titles = {
            "info": "Information",
            "success": "Success",
            "warning": "Warning",
            "error": "Error",
            "trade": "Trade Notification",
            "alert": "Price Alert"
        }
        
        message = custom_message or messages.get(notification_type, "Test notification")
        title = titles.get(notification_type, "Test")
        
        self.notification_center.add_notification(
            title, 
            message, 
            notification_type, 
            priority
        )
        
        print(f"✓ Notification added: {notification_type} - {priority}")
    
    def auto_test_notifications(self):
        """Automatically test notifications"""
        test_types = ["info", "success", "warning", "error", "trade", "alert"]
        test_type = test_types[self.test_counter % len(test_types)]
        priority = "high" if test_type in ["error", "trade", "alert"] else "normal"
        
        self.test_notification(test_type, priority, f"Auto test #{self.test_counter + 1}")
        self.test_counter += 1
        
        # Update progress bar
        self.progress_bar.setValue((self.test_counter * 10) % 100)
        
        print(f"✓ Auto test #{self.test_counter} completed")

def main():
    """Main test function"""
    print("Starting Phase 1 Integration Test...")
    
    app = QApplication(sys.argv)
    
    # Test window
    window = Phase1TestWindow()
    window.show()
    
    print("✓ Test window created and displayed")
    print("\nTest Instructions:")
    print("1. Try switching themes using the theme buttons")
    print("2. Test notifications using the notification buttons")
    print("3. Check the notification center on the left")
    print("4. Verify modern component styling")
    print("5. Auto-notifications will appear every 10 seconds")
    print("\nPress Ctrl+C to exit")
    
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n✓ Test completed successfully")
        sys.exit(0)

if __name__ == "__main__":
    main()
