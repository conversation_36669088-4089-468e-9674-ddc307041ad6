# Modern UI Components for EpiNn0x Trader Interface
# Reusable modern components with enhanced styling

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
import math

class ModernCard(QFrame):
    """Modern card component with shadow and rounded corners"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.title = title
        self.setup_card()
    
    def setup_card(self):
        """Setup card appearance"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            QFrame {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                border-radius: 12px;
                padding: 16px;
            }
        """)
        
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(16, 16, 16, 16)
        self.main_layout.setSpacing(12)
        
        # Title if provided
        if self.title:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: bold;
                    color: #00ff44;
                    margin-bottom: 8px;
                }
            """)
            self.main_layout.addWidget(title_label)
    
    def add_widget(self, widget):
        """Add widget to card"""
        self.main_layout.addWidget(widget)
    
    def add_layout(self, layout):
        """Add layout to card"""
        self.main_layout.addLayout(layout)

class ModernButton(QPushButton):
    """Enhanced button with modern styling and animations"""
    
    def __init__(self, text="", button_type="default", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_button()
    
    def setup_button(self):
        """Setup button styling based on type"""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 13px;
                text-align: center;
            }
        """
        
        if self.button_type == "primary":
            style = base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #00ff44, stop:1 #00cc33);
                    color: #000000;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #33ff66, stop:1 #00ff44);
                }
                QPushButton:pressed {
                    background: #00cc33;
                }
            """
        elif self.button_type == "danger":
            style = base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #dc3545, stop:1 #bd2130);
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #e55564, stop:1 #dc3545);
                }
                QPushButton:pressed {
                    background: #bd2130;
                }
            """
        elif self.button_type == "success":
            style = base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #28a745, stop:1 #1e7e34);
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #34ce57, stop:1 #28a745);
                }
                QPushButton:pressed {
                    background: #1e7e34;
                }
            """
        else:  # default
            style = base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #3a3a3a, stop:1 #2d2d2d);
                    color: #ffffff;
                    border: 1px solid #404040;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                               stop:0 #454545, stop:1 #3a3a3a);
                    border-color: #00ff44;
                }
                QPushButton:pressed {
                    background: #2d2d2d;
                }
            """
        
        self.setStyleSheet(style)

class ModernProgressBar(QProgressBar):
    """Enhanced progress bar with modern styling"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_progress_bar()
    
    def setup_progress_bar(self):
        """Setup progress bar styling"""
        self.setStyleSheet("""
            QProgressBar {
                border: 1px solid #404040;
                border-radius: 8px;
                background-color: #2d2d2d;
                text-align: center;
                font-weight: bold;
                color: #ffffff;
                height: 20px;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #00ff44, stop:1 #00cc33);
                border-radius: 7px;
                margin: 1px;
            }
        """)

class ModernSlider(QSlider):
    """Enhanced slider with modern styling"""
    
    def __init__(self, orientation=Qt.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setup_slider()
    
    def setup_slider(self):
        """Setup slider styling"""
        self.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #404040;
                height: 6px;
                background: #2d2d2d;
                border-radius: 3px;
            }
            
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #00ff44, stop:1 #00cc33);
                border: 1px solid #00cc33;
                width: 18px;
                margin: -6px 0;
                border-radius: 9px;
            }
            
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #33ff66, stop:1 #00ff44);
            }
            
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #00ff44, stop:1 #00cc33);
                border-radius: 3px;
            }
        """)

class ModernSpinBox(QSpinBox):
    """Enhanced spin box with modern styling"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_spinbox()
    
    def setup_spinbox(self):
        """Setup spin box styling"""
        self.setStyleSheet("""
            QSpinBox {
                background-color: #2d2d2d;
                border: 2px solid #404040;
                border-radius: 8px;
                padding: 8px 12px;
                color: #ffffff;
                font-size: 13px;
                font-weight: 500;
            }
            
            QSpinBox:focus {
                border-color: #00ff44;
                background-color: #3a3a3a;
            }
            
            QSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #404040;
                border-bottom: 1px solid #404040;
                border-top-right-radius: 6px;
                background: #3a3a3a;
            }
            
            QSpinBox::up-button:hover {
                background: #454545;
            }
            
            QSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                width: 20px;
                border-left: 1px solid #404040;
                border-top: 1px solid #404040;
                border-bottom-right-radius: 6px;
                background: #3a3a3a;
            }
            
            QSpinBox::down-button:hover {
                background: #454545;
            }
            
            QSpinBox::up-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-bottom: 4px solid #ffffff;
            }
            
            QSpinBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #ffffff;
            }
        """)

class ModernComboBox(QComboBox):
    """Enhanced combo box with modern styling"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_combobox()
    
    def setup_combobox(self):
        """Setup combo box styling"""
        self.setStyleSheet("""
            QComboBox {
                background-color: #2d2d2d;
                border: 2px solid #404040;
                border-radius: 8px;
                padding: 8px 12px;
                color: #ffffff;
                font-size: 13px;
                font-weight: 500;
                min-width: 100px;
            }
            
            QComboBox:focus {
                border-color: #00ff44;
                background-color: #3a3a3a;
            }
            
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ffffff;
                margin-right: 10px;
            }
            
            QComboBox QAbstractItemView {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                border-radius: 6px;
                selection-background-color: #00ff4433;
                color: #ffffff;
                padding: 4px;
            }
            
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border-radius: 4px;
            }
            
            QComboBox QAbstractItemView::item:hover {
                background-color: #3a3a3a;
            }
            
            QComboBox QAbstractItemView::item:selected {
                background-color: #00ff4433;
                color: #ffffff;
            }
        """)

class StatusIndicator(QLabel):
    """Status indicator with color-coded states"""
    
    def __init__(self, status="neutral", parent=None):
        super().__init__(parent)
        self.status = status
        self.setup_indicator()
    
    def setup_indicator(self):
        """Setup status indicator"""
        self.setFixedSize(12, 12)
        self.setStyleSheet("border-radius: 6px;")
        self.update_status(self.status)
    
    def update_status(self, status):
        """Update status and color"""
        self.status = status
        colors = {
            'online': '#28a745',
            'offline': '#dc3545',
            'warning': '#ffc107',
            'neutral': '#6c757d',
            'active': '#00ff44',
            'inactive': '#404040'
        }
        
        color = colors.get(status, '#6c757d')
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: 6px;
            }}
        """)

class ModernLineEdit(QLineEdit):
    """Enhanced line edit with modern styling"""
    
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        if placeholder:
            self.setPlaceholderText(placeholder)
        self.setup_lineedit()
    
    def setup_lineedit(self):
        """Setup line edit styling"""
        self.setStyleSheet("""
            QLineEdit {
                background-color: #2d2d2d;
                border: 2px solid #404040;
                border-radius: 8px;
                padding: 10px 12px;
                color: #ffffff;
                font-size: 13px;
                font-weight: 500;
            }
            
            QLineEdit:focus {
                border-color: #00ff44;
                background-color: #3a3a3a;
            }
            
            QLineEdit::placeholder {
                color: #888888;
            }
        """)
