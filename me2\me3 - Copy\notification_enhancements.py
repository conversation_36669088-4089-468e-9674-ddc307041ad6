# Enhanced Notification System for EpiNn0x Trader Interface
# Modern notification center with advanced features

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime, timedelta
import json
import os

class ModernNotificationCenter(QWidget):
    """Enhanced notification center with modern UI and advanced features"""
    
    notification_triggered = Signal(str, str, str, str)  # title, message, type, priority
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.notifications = []
        self.filtered_notifications = []
        self.max_notifications = 100
        self._updating_list = False
        self.setup_ui()
        self.setup_notification_rules()
        self.load_notification_history()
    
    def setup_ui(self):
        """Setup enhanced notification center UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # Header with search and controls
        header_layout = QHBoxLayout()
        
        # Title
        title = QLabel("Notification Center")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #00ff44;
                margin-bottom: 5px;
            }
        """)
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Unread count badge
        self.unread_badge = QLabel("0")
        self.unread_badge.setFixedSize(24, 24)
        self.unread_badge.setAlignment(Qt.AlignCenter)
        self.unread_badge.setStyleSheet("""
            QLabel {
                background-color: #dc3545;
                color: white;
                border-radius: 12px;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        header_layout.addWidget(self.unread_badge)
        
        layout.addLayout(header_layout)
        
        # Search and filter bar
        filter_layout = QHBoxLayout()
        
        # Search box
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search notifications...")
        self.search_box.textChanged.connect(self.filter_notifications)
        filter_layout.addWidget(self.search_box)
        
        # Type filter
        self.type_filter = QComboBox()
        self.type_filter.addItems(["All", "Info", "Success", "Warning", "Error", "Trade", "Alert"])
        self.type_filter.currentTextChanged.connect(self.filter_notifications)
        filter_layout.addWidget(self.type_filter)
        
        # Clear all button
        clear_btn = QPushButton("Clear All")
        clear_btn.setFixedSize(80, 32)
        clear_btn.clicked.connect(self.clear_all_notifications)
        filter_layout.addWidget(clear_btn)
        
        layout.addLayout(filter_layout)
        
        # Notification list with custom styling
        self.notification_list = QListWidget()
        self.notification_list.setStyleSheet("""
            QListWidget {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                border-radius: 8px;
                padding: 4px;
            }
            QListWidget::item {
                background-color: #3a3a3a;
                border: 1px solid #505050;
                border-radius: 6px;
                margin: 2px;
                padding: 0px;
            }
            QListWidget::item:hover {
                background-color: #454545;
                border-color: #00ff44;
            }
            QListWidget::item:selected {
                background-color: #505050;
                border-color: #00ff44;
            }
        """)
        layout.addWidget(self.notification_list)
        
        # Bottom controls
        bottom_layout = QHBoxLayout()
        
        # Mark all as read
        mark_read_btn = QPushButton("Mark All Read")
        mark_read_btn.clicked.connect(self.mark_all_read)
        bottom_layout.addWidget(mark_read_btn)
        
        # Export notifications
        export_btn = QPushButton("Export")
        export_btn.clicked.connect(self.export_notifications)
        bottom_layout.addWidget(export_btn)
        
        # Settings button
        settings_btn = QPushButton("Settings")
        settings_btn.clicked.connect(self.show_settings)
        bottom_layout.addWidget(settings_btn)
        
        layout.addLayout(bottom_layout)
    
    def setup_notification_rules(self):
        """Setup enhanced notification rules"""
        self.rules = {
            'price_alerts': [],
            'pnl_alerts': {
                'profit_threshold': 100.0,
                'loss_threshold': -50.0,
                'percentage_threshold': 10.0,
                'enabled': True
            },
            'position_alerts': {
                'large_position_threshold': 1000.0,
                'margin_threshold': 80.0,
                'enabled': True
            },
            'system_alerts': {
                'connection_issues': True,
                'order_failures': True,
                'margin_warnings': True,
                'api_errors': True
            },
            'sound_alerts': {
                'enabled': True,
                'volume': 50,
                'high_priority_only': False
            },
            'popup_settings': {
                'enabled': True,
                'duration': 5000,
                'position': 'top-right'
            }
        }
    
    def add_notification(self, title, message, notification_type='info', priority='normal', data=None):
        """Add enhanced notification with metadata"""
        timestamp = datetime.now()
        notification = {
            'id': len(self.notifications),
            'title': title,
            'message': message,
            'type': notification_type.lower(),
            'priority': priority,
            'timestamp': timestamp,
            'read': False,
            'data': data or {},
            'category': self._categorize_notification(title, message, notification_type)
        }
        
        self.notifications.append(notification)
        
        # Limit notification history
        if len(self.notifications) > self.max_notifications:
            self.notifications = self.notifications[-self.max_notifications:]
        
        self.update_notification_list()
        self.update_unread_count()
        
        # Show popup for high priority notifications
        if priority == 'high' or notification_type.lower() == 'error':
            self.show_popup_notification(notification)
        
        # Play sound if enabled
        if self.rules['sound_alerts']['enabled']:
            if not self.rules['sound_alerts']['high_priority_only'] or priority == 'high':
                self.play_notification_sound(notification_type)
        
        # Emit signal
        self.notification_triggered.emit(title, message, notification_type, priority)
        
        # Save to history
        self.save_notification_history()
    
    def _categorize_notification(self, title, message, notification_type):
        """Categorize notification for better organization"""
        title_lower = title.lower()
        message_lower = message.lower()
        _ = notification_type  # Acknowledge parameter

        if any(word in title_lower for word in ['trade', 'order', 'position']):
            return 'trading'
        elif any(word in title_lower for word in ['price', 'alert']):
            return 'market'
        elif any(word in title_lower for word in ['connection', 'api', 'error']):
            return 'system'
        elif any(word in title_lower for word in ['profit', 'loss', 'pnl']):
            return 'performance'
        elif any(word in message_lower for word in ['balance', 'equity', 'margin']):
            return 'account'
        else:
            return 'general'
    
    def create_notification_widget(self, notification):
        """Create enhanced widget for a single notification"""
        widget = QWidget()
        widget.setFixedHeight(80)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(12, 8, 12, 8)
        
        # Priority indicator
        priority_indicator = QWidget()
        priority_indicator.setFixedSize(4, 64)
        priority_color = {
            'low': '#6c757d',
            'normal': '#17a2b8',
            'high': '#dc3545',
            'critical': '#ff0000'
        }.get(notification['priority'], '#17a2b8')
        
        priority_indicator.setStyleSheet(f"""
            QWidget {{
                background-color: {priority_color};
                border-radius: 2px;
            }}
        """)
        layout.addWidget(priority_indicator)
        
        # Icon based on type and category
        icon_label = QLabel()
        icon_label.setFixedSize(32, 32)
        icon_label.setAlignment(Qt.AlignCenter)
        
        icons = {
            'info': '📝',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'trade': '💰',
            'alert': '🔔'
        }
        
        icon_label.setText(icons.get(notification['type'], '📝'))
        icon_label.setStyleSheet("font-size: 20px;")
        layout.addWidget(icon_label)
        
        # Content area
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)
        
        # Title and timestamp row
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        title_label = QLabel(notification['title'])
        title_label.setStyleSheet(f"""
            QLabel {{
                font-weight: bold;
                font-size: 13px;
                color: {'#ffffff' if not notification['read'] else '#cccccc'};
            }}
        """)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # Category badge
        category_badge = QLabel(notification['category'].upper())
        category_badge.setFixedHeight(16)
        category_badge.setStyleSheet("""
            QLabel {
                background-color: #404040;
                color: #cccccc;
                padding: 2px 6px;
                border-radius: 8px;
                font-size: 9px;
                font-weight: bold;
            }
        """)
        title_layout.addWidget(category_badge)
        
        time_label = QLabel(notification['timestamp'].strftime("%H:%M"))
        time_label.setStyleSheet("color: #888; font-size: 11px;")
        title_layout.addWidget(time_label)
        
        content_layout.addLayout(title_layout)
        
        # Message
        message_label = QLabel(notification['message'])
        message_label.setStyleSheet("color: #ccc; font-size: 12px;")
        message_label.setWordWrap(True)
        message_label.setMaximumHeight(32)
        content_layout.addWidget(message_label)
        
        layout.addLayout(content_layout)
        
        # Action buttons for certain notification types
        if notification['type'] in ['trade', 'alert']:
            action_btn = QPushButton("View")
            action_btn.setFixedSize(50, 24)
            action_btn.clicked.connect(lambda: self.handle_notification_action(notification))
            layout.addWidget(action_btn)
        
        # Read/unread indicator
        if not notification['read']:
            unread_dot = QLabel("●")
            unread_dot.setFixedSize(12, 12)
            unread_dot.setStyleSheet("color: #00ff44; font-size: 16px;")
            layout.addWidget(unread_dot)
        
        return widget

    def update_notification_list(self):
        """Update the notification list widget with filtering"""
        self.notification_list.clear()

        # Add filtered notifications (most recent first)
        for notification in reversed(self.filtered_notifications[-50:]):
            item = QListWidgetItem()
            widget = self.create_notification_widget(notification)
            item.setSizeHint(widget.sizeHint())

            self.notification_list.addItem(item)
            self.notification_list.setItemWidget(item, widget)

            # Connect click to mark as read
            widget.mousePressEvent = lambda event, n=notification: (self.mark_notification_read(n['id']), event)[0]

    def filter_notifications(self):
        """Filter notifications based on search and type"""
        search_text = self.search_box.text().lower()
        type_filter = self.type_filter.currentText().lower()

        self.filtered_notifications = []

        for notification in self.notifications:
            # Apply search filter
            if search_text and search_text not in notification['title'].lower() and search_text not in notification['message'].lower():
                continue

            # Apply type filter
            if type_filter != 'all' and notification['type'] != type_filter:
                continue

            self.filtered_notifications.append(notification)

        # Update list if this was called from filter controls
        if hasattr(self, 'notification_list') and hasattr(self, '_updating_list'):
            if not self._updating_list:
                self._updating_list = True
                self.update_notification_list()
                self._updating_list = False

    def update_unread_count(self):
        """Update unread notification count badge"""
        unread_count = sum(1 for n in self.notifications if not n['read'])
        self.unread_badge.setText(str(unread_count))
        self.unread_badge.setVisible(unread_count > 0)

    def mark_notification_read(self, notification_id):
        """Mark a specific notification as read"""
        for notification in self.notifications:
            if notification['id'] == notification_id:
                notification['read'] = True
                break
        self.update_unread_count()
        self.update_notification_list()

    def mark_all_read(self):
        """Mark all notifications as read"""
        for notification in self.notifications:
            notification['read'] = True
        self.update_unread_count()
        self.update_notification_list()

    def clear_all_notifications(self):
        """Clear all notifications"""
        self.notifications.clear()
        self.filtered_notifications.clear()
        self.update_notification_list()
        self.update_unread_count()
        self.save_notification_history()

    def handle_notification_action(self, notification):
        """Handle action button clicks for notifications"""
        # Emit signal for parent to handle specific actions
        self.notification_triggered.emit(
            f"action_{notification['type']}",
            notification['message'],
            notification['type'],
            'action'
        )

    def show_popup_notification(self, notification):
        """Show enhanced popup notification"""
        if self.rules['popup_settings']['enabled']:
            popup = EnhancedNotificationPopup(notification, self.parent())
            popup.show()

    def play_notification_sound(self, notification_type):
        """Play notification sound (placeholder for sound implementation)"""
        # This would integrate with a sound system
        # For now, just use system beep
        _ = notification_type  # Acknowledge parameter
        QApplication.beep()

    def export_notifications(self):
        """Export notifications to file"""
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Export Notifications",
            f"notifications_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON Files (*.json)"
        )

        if filename:
            try:
                export_data = []
                for notification in self.notifications:
                    export_notification = notification.copy()
                    export_notification['timestamp'] = notification['timestamp'].isoformat()
                    export_data.append(export_notification)

                with open(filename, 'w') as f:
                    json.dump(export_data, f, indent=2)

                QMessageBox.information(self, "Export Complete", f"Notifications exported to {filename}")
            except Exception as e:
                QMessageBox.warning(self, "Export Error", f"Failed to export notifications: {str(e)}")

    def save_notification_history(self):
        """Save notification history to file"""
        try:
            history_file = "notification_history.json"
            export_data = []

            # Only save last 50 notifications to keep file size manageable
            for notification in self.notifications[-50:]:
                export_notification = notification.copy()
                export_notification['timestamp'] = notification['timestamp'].isoformat()
                export_data.append(export_notification)

            with open(history_file, 'w') as f:
                json.dump(export_data, f, indent=2)
        except Exception as e:
            print(f"Failed to save notification history: {e}")

    def load_notification_history(self):
        """Load notification history from file"""
        try:
            history_file = "notification_history.json"
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    data = json.load(f)

                for item in data:
                    item['timestamp'] = datetime.fromisoformat(item['timestamp'])
                    self.notifications.append(item)

                self.update_notification_list()
                self.update_unread_count()
        except Exception as e:
            print(f"Failed to load notification history: {e}")

    def show_settings(self):
        """Show enhanced notification settings dialog"""
        dialog = EnhancedNotificationSettingsDialog(self.rules, self)
        if dialog.exec() == QDialog.Accepted:
            self.rules = dialog.get_rules()

class EnhancedNotificationPopup(QWidget):
    """Enhanced popup notification with modern styling"""

    def __init__(self, notification, parent=None):
        super().__init__(parent)
        self.notification = notification
        self.setup_popup()
        self.setup_animation()

    def setup_popup(self):
        """Setup enhanced popup appearance"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(380, 120)

        # Position based on settings
        screen = QApplication.primaryScreen().geometry()
        self.move(screen.width() - 390, 10)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Main frame with modern styling
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self._get_notification_color()},
                    stop:1 {self._get_notification_color_dark()});
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """)

        frame_layout = QHBoxLayout(frame)
        frame_layout.setContentsMargins(16, 12, 16, 12)

        # Enhanced icon with background
        icon_container = QWidget()
        icon_container.setFixedSize(48, 48)
        icon_container.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 24px;
            }
        """)

        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)

        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 24px; background: transparent;")

        icons = {
            'info': '📝',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'trade': '💰',
            'alert': '🔔'
        }

        icon_label.setText(icons.get(self.notification['type'], '📝'))
        icon_layout.addWidget(icon_label)

        frame_layout.addWidget(icon_container)

        # Content with better typography
        content_layout = QVBoxLayout()
        content_layout.setSpacing(4)

        title_label = QLabel(self.notification['title'])
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: bold;
                font-size: 15px;
                background: transparent;
            }
        """)
        content_layout.addWidget(title_label)

        message_label = QLabel(self.notification['message'])
        message_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 12px;
                background: transparent;
            }
        """)
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)

        # Timestamp
        time_label = QLabel(self.notification['timestamp'].strftime("%H:%M:%S"))
        time_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 10px;
                background: transparent;
            }
        """)
        content_layout.addWidget(time_label)

        frame_layout.addLayout(content_layout)

        # Modern close button
        close_btn = QPushButton("×")
        close_btn.setFixedSize(32, 32)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                font-size: 18px;
                font-weight: bold;
                border: none;
                border-radius: 16px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)
        close_btn.clicked.connect(self.close)
        frame_layout.addWidget(close_btn)

        layout.addWidget(frame)

        # Auto-close timer
        self.close_timer = QTimer()
        self.close_timer.timeout.connect(self.close)
        self.close_timer.start(5000)  # Auto-close after 5 seconds

    def _get_notification_color(self):
        """Get color based on notification type"""
        colors = {
            'error': '#dc3545',
            'success': '#28a745',
            'warning': '#ffc107',
            'info': '#17a2b8',
            'trade': '#28a745',
            'alert': '#ff6600'
        }
        return colors.get(self.notification['type'], '#17a2b8')

    def _get_notification_color_dark(self):
        """Get darker variant of notification color"""
        colors = {
            'error': '#bd2130',
            'success': '#1e7e34',
            'warning': '#e0a800',
            'info': '#138496',
            'trade': '#1e7e34',
            'alert': '#cc5200'
        }
        return colors.get(self.notification['type'], '#138496')

    def setup_animation(self):
        """Setup enhanced slide-in animation"""
        self.animation = QPropertyAnimation(self, b"pos")
        self.animation.setDuration(400)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)

        # Start position (off-screen)
        screen = QApplication.primaryScreen().geometry()
        start_pos = QPoint(screen.width(), 10)
        end_pos = QPoint(screen.width() - 390, 10)

        self.move(start_pos)
        self.animation.setStartValue(start_pos)
        self.animation.setEndValue(end_pos)
        self.animation.start()

class EnhancedNotificationSettingsDialog(QDialog):
    """Enhanced dialog for configuring notification settings"""

    def __init__(self, rules, parent=None):
        super().__init__(parent)
        self.rules = rules.copy()
        self.setup_dialog()

    def setup_dialog(self):
        """Setup enhanced settings dialog"""
        self.setWindowTitle("Notification Settings")
        self.setFixedSize(500, 600)
        self.setStyleSheet("""
            QDialog {
                background-color: #2d2d2d;
                color: #ffffff;
            }
        """)

        layout = QVBoxLayout(self)

        # Tab widget for different categories
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #404040;
                border-radius: 8px;
                background-color: #3a3a3a;
            }
            QTabBar::tab {
                background: #2d2d2d;
                border: 1px solid #404040;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background: #00ff44;
                color: #000000;
                font-weight: bold;
            }
        """)

        # P&L Alerts tab
        pnl_tab = self.create_pnl_tab()
        tabs.addTab(pnl_tab, "P&L Alerts")

        # Price Alerts tab
        price_tab = self.create_price_tab()
        tabs.addTab(price_tab, "Price Alerts")

        # System Alerts tab
        system_tab = self.create_system_tab()
        tabs.addTab(system_tab, "System Alerts")

        # Sound & Popup tab
        sound_tab = self.create_sound_tab()
        tabs.addTab(sound_tab, "Sound & Popup")

        layout.addWidget(tabs)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def create_pnl_tab(self):
        """Create P&L alerts configuration tab"""
        tab = QWidget()
        layout = QFormLayout(tab)

        self.pnl_enabled = QCheckBox("Enable P&L Alerts")
        self.pnl_enabled.setChecked(self.rules['pnl_alerts']['enabled'])
        layout.addRow(self.pnl_enabled)

        self.profit_threshold = QDoubleSpinBox()
        self.profit_threshold.setRange(1, 100000)
        self.profit_threshold.setValue(self.rules['pnl_alerts']['profit_threshold'])
        self.profit_threshold.setPrefix("$")
        layout.addRow("Profit Alert Threshold:", self.profit_threshold)

        self.loss_threshold = QDoubleSpinBox()
        self.loss_threshold.setRange(-100000, -1)
        self.loss_threshold.setValue(self.rules['pnl_alerts']['loss_threshold'])
        self.loss_threshold.setPrefix("$")
        layout.addRow("Loss Alert Threshold:", self.loss_threshold)

        self.percentage_threshold = QDoubleSpinBox()
        self.percentage_threshold.setRange(1, 100)
        self.percentage_threshold.setValue(self.rules['pnl_alerts']['percentage_threshold'])
        self.percentage_threshold.setSuffix("%")
        layout.addRow("Percentage Change Threshold:", self.percentage_threshold)

        return tab

    def create_price_tab(self):
        """Create price alerts configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        layout.addWidget(QLabel("Price Alerts:"))

        self.price_alerts_list = QListWidget()
        layout.addWidget(self.price_alerts_list)

        # Add price alert controls
        add_layout = QHBoxLayout()
        add_layout.addWidget(QLabel("Symbol:"))
        self.price_symbol = QLineEdit()
        add_layout.addWidget(self.price_symbol)

        add_layout.addWidget(QLabel("Price:"))
        self.price_value = QDoubleSpinBox()
        self.price_value.setRange(0.0001, 1000000)
        self.price_value.setDecimals(4)
        add_layout.addWidget(self.price_value)

        add_price_btn = QPushButton("Add Alert")
        add_price_btn.clicked.connect(self.add_price_alert)
        add_layout.addWidget(add_price_btn)

        layout.addLayout(add_layout)

        # Load existing alerts
        self.load_price_alerts()

        return tab

    def create_system_tab(self):
        """Create system alerts configuration tab"""
        tab = QWidget()
        layout = QFormLayout(tab)

        self.connection_alerts = QCheckBox("Connection Issues")
        self.connection_alerts.setChecked(self.rules['system_alerts']['connection_issues'])
        layout.addRow(self.connection_alerts)

        self.order_failure_alerts = QCheckBox("Order Failures")
        self.order_failure_alerts.setChecked(self.rules['system_alerts']['order_failures'])
        layout.addRow(self.order_failure_alerts)

        self.margin_alerts = QCheckBox("Margin Warnings")
        self.margin_alerts.setChecked(self.rules['system_alerts']['margin_warnings'])
        layout.addRow(self.margin_alerts)

        self.api_error_alerts = QCheckBox("API Errors")
        self.api_error_alerts.setChecked(self.rules['system_alerts']['api_errors'])
        layout.addRow(self.api_error_alerts)

        return tab

    def create_sound_tab(self):
        """Create sound and popup configuration tab"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # Sound settings
        layout.addRow(QLabel("Sound Settings:"))

        self.sound_enabled = QCheckBox("Enable Sound Alerts")
        self.sound_enabled.setChecked(self.rules['sound_alerts']['enabled'])
        layout.addRow(self.sound_enabled)

        self.sound_volume = QSlider(Qt.Horizontal)
        self.sound_volume.setRange(0, 100)
        self.sound_volume.setValue(self.rules['sound_alerts']['volume'])
        layout.addRow("Volume:", self.sound_volume)

        self.high_priority_only = QCheckBox("High Priority Only")
        self.high_priority_only.setChecked(self.rules['sound_alerts']['high_priority_only'])
        layout.addRow(self.high_priority_only)

        # Popup settings
        layout.addRow(QLabel("Popup Settings:"))

        self.popup_enabled = QCheckBox("Enable Popup Notifications")
        self.popup_enabled.setChecked(self.rules['popup_settings']['enabled'])
        layout.addRow(self.popup_enabled)

        self.popup_duration = QSpinBox()
        self.popup_duration.setRange(1000, 30000)
        self.popup_duration.setValue(self.rules['popup_settings']['duration'])
        self.popup_duration.setSuffix(" ms")
        layout.addRow("Display Duration:", self.popup_duration)

        return tab

    def load_price_alerts(self):
        """Load existing price alerts"""
        for alert in self.rules['price_alerts']:
            item_text = f"{alert['symbol']} @ ${alert['price']:.4f}"
            self.price_alerts_list.addItem(item_text)

    def add_price_alert(self):
        """Add new price alert"""
        symbol = self.price_symbol.text().strip()
        price = self.price_value.value()

        if symbol and price > 0:
            alert = {
                'symbol': symbol,
                'price': price,
                'condition': 'above'
            }
            self.rules['price_alerts'].append(alert)

            item_text = f"{symbol} @ ${price:.4f}"
            self.price_alerts_list.addItem(item_text)

            self.price_symbol.clear()
            self.price_value.setValue(0)

    def get_rules(self):
        """Get updated rules"""
        # Update P&L rules
        self.rules['pnl_alerts']['enabled'] = self.pnl_enabled.isChecked()
        self.rules['pnl_alerts']['profit_threshold'] = self.profit_threshold.value()
        self.rules['pnl_alerts']['loss_threshold'] = self.loss_threshold.value()
        self.rules['pnl_alerts']['percentage_threshold'] = self.percentage_threshold.value()

        # Update system rules
        self.rules['system_alerts']['connection_issues'] = self.connection_alerts.isChecked()
        self.rules['system_alerts']['order_failures'] = self.order_failure_alerts.isChecked()
        self.rules['system_alerts']['margin_warnings'] = self.margin_alerts.isChecked()
        self.rules['system_alerts']['api_errors'] = self.api_error_alerts.isChecked()

        # Update sound rules
        self.rules['sound_alerts']['enabled'] = self.sound_enabled.isChecked()
        self.rules['sound_alerts']['volume'] = self.sound_volume.value()
        self.rules['sound_alerts']['high_priority_only'] = self.high_priority_only.isChecked()

        # Update popup rules
        self.rules['popup_settings']['enabled'] = self.popup_enabled.isChecked()
        self.rules['popup_settings']['duration'] = self.popup_duration.value()

        return self.rules
