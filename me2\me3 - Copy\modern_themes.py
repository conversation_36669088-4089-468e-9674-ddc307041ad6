# Modern Theme System for EpiNn0x Trader Interface
# Professional styling with enhanced visual hierarchy

from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QColor, QPalette
import colorsys

class ColorUtils:
    """Utility class for color manipulation"""
    
    @staticmethod
    def hex_to_rgb(hex_color):
        """Convert hex color to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    @staticmethod
    def rgb_to_hex(rgb):
        """Convert RGB tuple to hex color"""
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
    
    @staticmethod
    def lighten_color(hex_color, percent):
        """Lighten a hex color by percentage"""
        rgb = ColorUtils.hex_to_rgb(hex_color)
        h, l, s = colorsys.rgb_to_hls(rgb[0]/255.0, rgb[1]/255.0, rgb[2]/255.0)
        l = min(1.0, l + (percent / 100.0))
        rgb = colorsys.hls_to_rgb(h, l, s)
        return ColorUtils.rgb_to_hex(tuple(int(c * 255) for c in rgb))
    
    @staticmethod
    def darken_color(hex_color, percent):
        """Darken a hex color by percentage"""
        rgb = ColorUtils.hex_to_rgb(hex_color)
        h, l, s = colorsys.rgb_to_hls(rgb[0]/255.0, rgb[1]/255.0, rgb[2]/255.0)
        l = max(0.0, l - (percent / 100.0))
        rgb = colorsys.hls_to_rgb(h, l, s)
        return ColorUtils.rgb_to_hex(tuple(int(c * 255) for c in rgb))
    
    @staticmethod
    def add_alpha(hex_color, alpha):
        """Add alpha channel to hex color"""
        alpha_hex = format(int(alpha * 255), '02x')
        return f"{hex_color}{alpha_hex}"

class ModernTheme:
    """Base class for modern themes"""
    
    def __init__(self, name, colors):
        self.name = name
        self.colors = colors
        self._generate_variants()
    
    def _generate_variants(self):
        """Generate color variants for hover, pressed states"""
        self.colors['primary_hover'] = ColorUtils.lighten_color(self.colors['primary'], 15)
        self.colors['primary_pressed'] = ColorUtils.darken_color(self.colors['primary'], 20)
        self.colors['surface_hover'] = ColorUtils.lighten_color(self.colors['surface'], 10)
        self.colors['surface_pressed'] = ColorUtils.darken_color(self.colors['surface'], 15)
        self.colors['background_light'] = ColorUtils.lighten_color(self.colors['background'], 5)
        self.colors['border_light'] = ColorUtils.lighten_color(self.colors['border'], 20)
        
        # Generate alpha variants
        self.colors['primary_alpha'] = ColorUtils.add_alpha(self.colors['primary'], 0.2)
        self.colors['secondary_alpha'] = ColorUtils.add_alpha(self.colors['secondary'], 0.2)
        self.colors['accent_alpha'] = ColorUtils.add_alpha(self.colors['accent'], 0.2)
    
    def get_color(self, key):
        """Get color by key"""
        return self.colors.get(key, '#ffffff')

# Define the three professional themes
DARK_PROFESSIONAL = ModernTheme("Dark Professional", {
    'background': '#1a1a1a',
    'surface': '#2d2d2d',
    'surface_elevated': '#3a3a3a',
    'primary': '#00ff44',
    'secondary': '#0099ff',
    'accent': '#ff6600',
    'text': '#ffffff',
    'text_secondary': '#cccccc',
    'text_muted': '#999999',
    'border': '#404040',
    'hover': '#404040',
    'selection': '#0099ff33',
    'success': '#28a745',
    'warning': '#ffc107',
    'error': '#dc3545',
    'info': '#17a2b8'
})

CYBERPUNK = ModernTheme("Cyberpunk", {
    'background': '#0a0a0a',
    'surface': '#1a1a2e',
    'surface_elevated': '#16213e',
    'primary': '#00ff41',
    'secondary': '#ff0080',
    'accent': '#00d4ff',
    'text': '#ffffff',
    'text_secondary': '#b3b3b3',
    'text_muted': '#808080',
    'border': '#16213e',
    'hover': '#0f3460',
    'selection': '#ff008033',
    'success': '#00ff41',
    'warning': '#ffff00',
    'error': '#ff0080',
    'info': '#00d4ff'
})

MODERN_LIGHT = ModernTheme("Modern Light", {
    'background': '#ffffff',
    'surface': '#f8f9fa',
    'surface_elevated': '#ffffff',
    'primary': '#2196f3',
    'secondary': '#4caf50',
    'accent': '#ff9800',
    'text': '#212121',
    'text_secondary': '#757575',
    'text_muted': '#9e9e9e',
    'border': '#e0e0e0',
    'hover': '#f5f5f5',
    'selection': '#2196f333',
    'success': '#4caf50',
    'warning': '#ff9800',
    'error': '#f44336',
    'info': '#2196f3'
})

# Theme registry
THEMES = {
    'dark_professional': DARK_PROFESSIONAL,
    'cyberpunk': CYBERPUNK,
    'modern_light': MODERN_LIGHT
}

class ThemeManager(QObject):
    """Manages theme switching and stylesheet generation"""
    
    theme_changed = Signal(str)  # Emits theme name when changed
    
    def __init__(self):
        super().__init__()
        self.current_theme = DARK_PROFESSIONAL
        self.theme_name = 'dark_professional'
    
    def set_theme(self, theme_name):
        """Set the current theme"""
        if theme_name in THEMES:
            self.current_theme = THEMES[theme_name]
            self.theme_name = theme_name
            self.theme_changed.emit(theme_name)
            return True
        return False
    
    def get_current_theme(self):
        """Get the current theme"""
        return self.current_theme
    
    def get_theme_names(self):
        """Get list of available theme names"""
        return list(THEMES.keys())
    
    def generate_stylesheet(self):
        """Generate complete stylesheet for current theme"""
        theme = self.current_theme

        return f"""
        /* ===== MAIN WINDOW ===== */
        QMainWindow {{
            background-color: {theme.get_color('background')};
            color: {theme.get_color('text')};
            font-family: 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            font-size: 13px;
        }}

        QWidget {{
            background-color: transparent;
            color: {theme.get_color('text')};
        }}

        /* ===== ENHANCED BUTTONS ===== */
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 {theme.get_color('surface')},
                                       stop:1 {theme.get_color('surface_pressed')});
            border: 1px solid {theme.get_color('border')};
            color: {theme.get_color('text')};
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 13px;
            min-height: 16px;
            text-align: center;
        }}

        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 {theme.get_color('surface_hover')},
                                       stop:1 {theme.get_color('surface')});
            border-color: {theme.get_color('primary')};
        }}

        QPushButton:pressed {{
            background: {theme.get_color('surface_pressed')};
            border-color: {theme.get_color('primary')};
        }}

        QPushButton:disabled {{
            background: {theme.get_color('surface')};
            color: {theme.get_color('text_muted')};
            border-color: {theme.get_color('border')};
        }}

        /* ===== TRADING ACTION BUTTONS ===== */
        QPushButton[action="buy"] {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 #28a745, stop:1 #1e7e34);
            border: 2px solid #1e7e34;
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 12px 24px;
            border-radius: 8px;
        }}

        QPushButton[action="buy"]:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 #34ce57, stop:1 #28a745);
            border-color: #34ce57;
        }}

        QPushButton[action="sell"] {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 #dc3545, stop:1 #bd2130);
            border: 2px solid #bd2130;
            color: white;
            font-weight: bold;
            font-size: 14px;
            padding: 12px 24px;
            border-radius: 8px;
        }}

        QPushButton[action="sell"]:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 #e55564, stop:1 #dc3545);
            border-color: #e55564;
        }}

        /* ===== MODERN CARDS/GROUPS ===== */
        QGroupBox {{
            background-color: {theme.get_color('surface')};
            border: 1px solid {theme.get_color('border')};
            border-radius: 12px;
            font-weight: bold;
            font-size: 14px;
            padding-top: 25px;
            margin-top: 15px;
            color: {theme.get_color('text')};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            subcontrol-position: top left;
            padding: 5px 15px;
            color: {theme.get_color('primary')};
            background-color: {theme.get_color('surface')};
            border-radius: 6px;
            border: 1px solid {theme.get_color('border')};
        }}

        /* ===== ENHANCED TABLES ===== */
        QTableWidget {{
            background-color: {theme.get_color('surface')};
            alternate-background-color: {theme.get_color('surface_hover')};
            gridline-color: {theme.get_color('border')};
            border: 1px solid {theme.get_color('border')};
            border-radius: 8px;
            selection-background-color: {theme.get_color('selection')};
            font-size: 12px;
        }}

        QTableWidget::item {{
            padding: 10px;
            border-bottom: 1px solid {theme.get_color('border')};
        }}

        QTableWidget::item:selected {{
            background-color: {theme.get_color('selection')};
            color: {theme.get_color('text')};
        }}

        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 {theme.get_color('surface_elevated')},
                                       stop:1 {theme.get_color('surface')});
            color: {theme.get_color('primary')};
            padding: 12px;
            border: none;
            border-right: 1px solid {theme.get_color('border')};
            font-weight: bold;
            font-size: 13px;
        }}
        """

    def generate_tab_styles(self):
        """Generate modern tab styles"""
        theme = self.current_theme

        return f"""
        /* ===== MODERN TABS ===== */
        QTabWidget::pane {{
            border: 1px solid {theme.get_color('border')};
            border-radius: 8px;
            background-color: {theme.get_color('surface')};
            margin-top: -1px;
        }}

        QTabBar::tab {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 {theme.get_color('surface')},
                                       stop:1 {theme.get_color('surface_pressed')});
            border: 1px solid {theme.get_color('border')};
            padding: 12px 24px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            color: {theme.get_color('text_secondary')};
            font-weight: 500;
            font-size: 13px;
        }}

        QTabBar::tab:selected {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 {theme.get_color('primary')},
                                       stop:1 {theme.get_color('primary_pressed')});
            color: {theme.get_color('background')};
            font-weight: bold;
            border-bottom: none;
        }}

        QTabBar::tab:hover:!selected {{
            background: {theme.get_color('surface_hover')};
            color: {theme.get_color('text')};
        }}
        """

    def generate_input_styles(self):
        """Generate enhanced input field styles"""
        theme = self.current_theme

        return f"""
        /* ===== ENHANCED INPUT FIELDS ===== */
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {theme.get_color('surface')};
            border: 2px solid {theme.get_color('border')};
            border-radius: 8px;
            padding: 10px 12px;
            color: {theme.get_color('text')};
            font-size: 13px;
            font-weight: 500;
        }}

        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border-color: {theme.get_color('primary')};
            background-color: {theme.get_color('surface_hover')};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {theme.get_color('text_secondary')};
            margin-right: 10px;
        }}

        QComboBox QAbstractItemView {{
            background-color: {theme.get_color('surface')};
            border: 1px solid {theme.get_color('border')};
            border-radius: 6px;
            selection-background-color: {theme.get_color('selection')};
            color: {theme.get_color('text')};
        }}

        /* ===== STATUS INDICATORS ===== */
        QLabel[status="profit"] {{
            color: {theme.get_color('success')};
            font-weight: bold;
        }}

        QLabel[status="loss"] {{
            color: {theme.get_color('error')};
            font-weight: bold;
        }}

        QLabel[status="neutral"] {{
            color: {theme.get_color('text_secondary')};
        }}

        QLabel[status="warning"] {{
            color: {theme.get_color('warning')};
            font-weight: bold;
        }}

        QLabel[status="info"] {{
            color: {theme.get_color('info')};
            font-weight: bold;
        }}
        """

    def generate_toolbar_styles(self):
        """Generate modern toolbar styles"""
        theme = self.current_theme

        return f"""
        /* ===== MODERN TOOLBAR ===== */
        QToolBar {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                       stop:0 {theme.get_color('surface_elevated')},
                                       stop:1 {theme.get_color('surface')});
            border: none;
            border-bottom: 1px solid {theme.get_color('border')};
            padding: 6px;
            spacing: 8px;
        }}

        QToolButton {{
            background: transparent;
            border: 1px solid transparent;
            border-radius: 6px;
            padding: 8px 16px;
            color: {theme.get_color('text')};
            font-weight: 500;
        }}

        QToolButton:hover {{
            background-color: {theme.get_color('surface_hover')};
            border-color: {theme.get_color('border')};
        }}

        QToolButton:pressed {{
            background-color: {theme.get_color('surface_pressed')};
            border-color: {theme.get_color('primary')};
        }}

        /* ===== MENU STYLES ===== */
        QMenuBar {{
            background: {theme.get_color('surface')};
            color: {theme.get_color('text')};
            border-bottom: 1px solid {theme.get_color('border')};
            padding: 4px;
        }}

        QMenuBar::item {{
            background: transparent;
            padding: 8px 16px;
            border-radius: 4px;
        }}

        QMenuBar::item:selected {{
            background: {theme.get_color('surface_hover')};
            color: {theme.get_color('primary')};
        }}

        QMenu {{
            background: {theme.get_color('surface')};
            color: {theme.get_color('text')};
            border: 1px solid {theme.get_color('border')};
            border-radius: 6px;
            padding: 4px;
        }}

        QMenu::item {{
            padding: 8px 20px;
            border-radius: 4px;
        }}

        QMenu::item:selected {{
            background: {theme.get_color('surface_hover')};
            color: {theme.get_color('primary')};
        }}
        """

    def get_complete_stylesheet(self):
        """Get complete stylesheet combining all components"""
        return (
            self.generate_stylesheet() +
            self.generate_tab_styles() +
            self.generate_input_styles() +
            self.generate_toolbar_styles()
        )
